#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Bilibili视频下载器模块
负责从Bilibili下载视频内容
"""

import os
import yt_dlp
from typing import Dict, Any, Optional, Callable
from config import Config
from utils.cookie_updater import CookieUpdater


class BilibiliDownloader:
    """
    Bilibili视频下载器类
    用于从Bilibili下载视频内容
    """

    def __init__(self, output_dir: Optional[str] = None):
        """
        初始化下载器

        Args:
            output_dir: 下载文件保存目录，None使用配置文件默认值
        """
        self.output_dir = str(output_dir or Config.DOWNLOAD_DIR)
        self.cookie_updater = CookieUpdater()

        # 确保输出目录存在
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)

    def validate_url(self, url: str) -> bool:
        """
        验证URL是否为有效的Bilibili链接

        Args:
            url: 要验证的URL

        Returns:
            bool: URL是否有效
        """
        # 简单验证URL格式
        valid_domains = ["bilibili.com", "b23.tv", "www.bilibili.com"]
        return any(domain in url for domain in valid_domains)

    def get_video_info(self, url: str) -> Dict[str, Any]:
        """
        获取视频信息

        Args:
            url: Bilibili视频URL

        Returns:
            Dict: 包含视频信息的字典
        """
        if not self.validate_url(url):
            raise ValueError(f"无效的Bilibili URL: {url}")

        ydl_opts = {
            'quiet': True,
            'no_warnings': True,
            'skip_download': True,
        }

        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            info = ydl.extract_info(url, download=False)
            return info

    def download_video(self, url: str, progress_callback: Optional[Callable] = None) -> str:
        """
        下载Bilibili视频

        Args:
            url: Bilibili视频URL
            progress_callback: 进度回调函数，接收下载进度百分比

        Returns:
            str: 下载文件的路径
        """
        if not self.validate_url(url):
            raise ValueError(f"无效的Bilibili URL: {url}")

        # 定义进度钩子
        def progress_hook(d):
            if d['status'] == 'downloading' and progress_callback:
                # 计算下载进度
                if d.get('total_bytes'):
                    percent = d['downloaded_bytes'] / d['total_bytes'] * 100
                    progress_callback(percent)
                elif d.get('total_bytes_estimate'):
                    percent = d['downloaded_bytes'] / d['total_bytes_estimate'] * 100
                    progress_callback(percent)

        # 设置下载选项
        ydl_opts = {
            'format': Config.DOWNLOAD_FORMAT,  # 从配置获取格式
            'outtmpl': os.path.join(self.output_dir, '%(title)s.%(ext)s'),
            'progress_hooks': [progress_hook],
            'quiet': False,
            'no_warnings': True,
        }

        # 执行下载
        try:
            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                info = ydl.extract_info(url, download=True)
                if info is None:
                    raise ValueError("extract_info 返回 None")
        except Exception as e:
            error_msg = str(e)
            if "Fresh cookies" in error_msg or "cookies" in error_msg.lower() or info is None:
                print("首次提取失败，尝试更新 cookies...")
                # 尝试更新 cookies
                if self.cookie_updater.update_for("bilibili"):
                    # 重新加载 cookies
                    cookie_file = self.cookie_updater.cookiefile_path()
                    if cookie_file:
                        ydl_opts['cookiefile'] = cookie_file
                    # 重试下载
                    try:
                        with yt_dlp.YoutubeDL(ydl_opts) as ydl_retry:
                            info = ydl_retry.extract_info(url, download=True)
                            if info is None:
                                raise ValueError(f"更新 cookies 后仍无法提取视频信息。可能需要手动更新 cookies 或检查 URL。URL: {url}")
                    except Exception as retry_e:
                        raise ValueError(f"更新 cookies 后重试仍失败: {retry_e}")
                else:
                    raise ValueError(f"无法更新 cookies，原始错误: {e}")
            else:
                # 不是 cookie 相关错误，直接抛出
                raise e

        # 获取下载后的文件路径
        filename = ydl.prepare_filename(info)
        return filename