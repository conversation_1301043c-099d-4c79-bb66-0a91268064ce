#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
通用 Cookies 更新模块
从浏览器提取 cookies 并导出为 cookie 文件，供各下载器共用
"""

import os
from typing import List, Tuple, Optional
import yt_dlp
from config import Config


class CookieUpdater:
    """通用 Cookies 更新器，可用于 YouTube/Bilibili/Douyin 等站点"""

    def __init__(self, cookie_output: Optional[str] = None, browsers: Optional[List[Tuple[str, ...]]] = None):
        self.cookie_output = cookie_output or Config.YOUTUBE_COOKIES_FILE or "cookies.txt"
        self.browsers = browsers or self._default_browsers()

    def _default_browsers(self) -> List[Tuple[str, ...]]:
        browsers: List[Tuple[str, ...]] = []
        firefox_profile = os.getenv('FIREFOX_PROFILE')
        if firefox_profile:
            browsers.append(('firefox', firefox_profile))
        else:
            browsers.append(('firefox',))
        browsers.extend([
            ('edge',),
            ('chromium',),
            ('chrome',),
        ])
        return browsers

    def _provider_test_url(self, provider: str) -> str:
        p = provider.lower()
        if p.startswith('you'):  # youtube
            return 'https://www.youtube.com/'
        if p.startswith('bili'):
            # 对于 Bilibili，使用一个简单的视频页面而不是首页
            return 'https://www.bilibili.com/video/BV1xx411c7mu'  # 一个经典的测试视频
        if p.startswith('dou') or p.startswith('dy'):
            # 对于 Douyin，使用一个具体的视频链接而不是首页
            return 'https://www.douyin.com/video/7000000000000000000'  # 占位符，实际使用时会被替换
        # 默认给一个常见站点，主要用于触发 cookies 提取
        return 'https://www.youtube.com/'

    def update_for(self, provider: str) -> bool:
        """
        为指定平台更新 cookies 并写入到 cookie_output
        返回是否更新成功
        """
        # 对于非 YouTube 平台，如果浏览器 cookie 提取失败，尝试使用现有的 YouTube cookies
        if provider.lower() != "youtube" and os.path.exists(self.cookie_output):
            print(f"检测到现有 cookies 文件，尝试复用于 {provider}")
            return True

        test_url = self._provider_test_url(provider)
        cookies_updated = False

        # 对于 Douyin 和 Bilibili，优先尝试 Firefox（通常更稳定）
        browser_order = self.browsers
        if provider.lower() in ["douyin", "bilibili"]:
            # 将 Firefox 放在最前面
            firefox_browsers = [b for b in self.browsers if b[0] == 'firefox']
            other_browsers = [b for b in self.browsers if b[0] != 'firefox']
            browser_order = firefox_browsers + other_browsers

        for c_browser in browser_order:
            try:
                print(f"正在从 {c_browser[0]} 浏览器更新 {provider} cookies...")
                ydl_opts = {
                    'quiet': True,
                    'skip_download': True,
                    'cookiesfrombrowser': c_browser,  # 必须是 tuple，避免被当作字符序列
                    'cookiefile': self.cookie_output,
                    'no_warnings': True,
                }

                # 对于 Douyin 和 Bilibili，如果测试 URL 失败，尝试使用 YouTube 来提取通用 cookies
                try:
                    with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                        ydl.extract_info(test_url, download=False)
                except Exception as url_error:
                    if provider.lower() in ["douyin", "bilibili"]:
                        print(f"  使用 {provider} URL 失败，尝试使用 YouTube URL 提取通用 cookies...")
                        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                            ydl.extract_info('https://www.youtube.com/', download=False)
                    else:
                        raise url_error

                if os.path.exists(self.cookie_output):
                    print(f"已保存 cookies -> {self.cookie_output}")
                    cookies_updated = True
                    break
            except Exception as e:
                print(f"浏览器配置 {c_browser} 更新 {provider} cookies 失败: {type(e).__name__}: {e!r}")

        if not cookies_updated:
            print(f"自动从浏览器更新 {provider} cookies 失败，请确保浏览器已完全关闭或手动导出 cookies 到 {self.cookie_output}。")
            print(f"提示：您可以手动导出浏览器 cookies 到 {self.cookie_output} 文件")
        return cookies_updated

    def cookiefile_path(self) -> Optional[str]:
        return self.cookie_output if self.cookie_output and os.path.exists(self.cookie_output) else None

