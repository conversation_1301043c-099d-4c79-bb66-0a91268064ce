#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
YouTube视频下载器模块
负责从YouTube下载视频内容
"""

import os
import yt_dlp
import subprocess
from typing import Dict, Any, Optional, Callable
from config import Config
from utils.cookie_updater import CookieUpdater


class YouTubeDownloader:
    """
    YouTube视频下载器类
    用于从YouTube下载视频内容
    """
    
    def __init__(self, output_dir: Optional[str] = None):
        """
        初始化下载器

        Args:
            output_dir: 下载文件保存目录，None使用配置文件默认值
        """
        self.output_dir = str(output_dir or Config.DOWNLOAD_DIR)
        self.cookie_updater = CookieUpdater()

        # 确保输出目录存在
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
    
    def validate_url(self, url: str) -> bool:
        """
        验证URL是否为有效的YouTube链接

        Args:
            url: 要验证的URL

        Returns:
            bool: URL是否有效
        """
        # 简单验证URL格式
        valid_domains = ["youtube.com", "youtu.be", "www.youtube.com"]
        return any(domain in url for domain in valid_domains)

    def update_cookies_from_browser(self) -> bool:
        """
        从浏览器自动更新YouTube cookies

        Returns:
            bool: 更新是否成功
        """
        if not Config.YOUTUBE_AUTO_UPDATE_COOKIES:
            return False
        return self.cookie_updater.update_for("youtube")
    
    def get_video_info(self, url: str) -> Dict[str, Any]:
        """
        获取视频信息
        
        Args:
            url: YouTube视频URL
            
        Returns:
            Dict: 包含视频信息的字典
        """
        if not self.validate_url(url):
            raise ValueError(f"无效的YouTube URL: {url}")
        
        ydl_opts = {
            'quiet': True,
            'no_warnings': True,
            'skip_download': True,
        }
        
        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            info = ydl.extract_info(url, download=False)
            return info
    
    def download_video(self, url: str, progress_callback: Optional[Callable] = None) -> str:
        """
        下载YouTube视频
        
        Args:
            url: YouTube视频URL
            progress_callback: 进度回调函数，接收下载进度百分比
            
        Returns:
            str: 下载文件的路径
        """
        if not self.validate_url(url):
            raise ValueError(f"无效的YouTube URL: {url}")
        
        # 定义进度钩子
        def progress_hook(d):
            if d['status'] == 'downloading' and progress_callback:
                # 计算下载进度
                if d.get('total_bytes'):
                    percent = d['downloaded_bytes'] / d['total_bytes'] * 100
                    progress_callback(percent)
                elif d.get('total_bytes_estimate'):
                    percent = d['downloaded_bytes'] / d['total_bytes_estimate'] * 100
                    progress_callback(percent)
        
        # 设置下载选项
        ydl_opts = {
            'format': Config.DOWNLOAD_FORMAT,  # 从配置获取格式
            'outtmpl': os.path.join(self.output_dir, '%(title)s.%(ext)s'),
            'progress_hooks': [progress_hook],
            'quiet': False,
            'no_warnings': True,
            'no_check_certificates': True,  # 跳过证书检查
            'ignoreerrors': True,  # 忽略错误继续
            'extract_flat': False,  # 确保提取完整信息
            'noplaylist': True,  # 仅下载单个视频
            'extractor_args': {  # 强制使用 Web 客户端
                'youtube': {
                    'player_client': ['web']
                }
            },
            'http_headers': {  # 显式使用常见浏览器 UA，降低风控
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/124.0 Safari/537.36'
            },
        }

        # 优先直接从浏览器注入 cookies（Firefox 优先；可通过 FIREFOX_PROFILE 指定）
        firefox_profile = os.getenv('FIREFOX_PROFILE')
        if firefox_profile:
            ydl_opts['cookiesfrombrowser'] = ('firefox', firefox_profile)
        else:
            ydl_opts['cookiesfrombrowser'] = ('firefox',)

        # 添加代理支持
        if Config.USE_PROXY and Config.PROXY_URL:
            ydl_opts['proxy'] = Config.PROXY_URL
        
        # 执行下载
        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            info = ydl.extract_info(url, download=True)
            if info is None:
                print("首次提取失败，尝试更新 cookies...")
                # 尝试更新 cookies
                if self.update_cookies_from_browser():
                    # 重新加载 cookies
                    cookie_file = self.cookie_updater.cookiefile_path()
                    if cookie_file:
                        ydl_opts['cookiefile'] = cookie_file
                    # 重试下载
                    with yt_dlp.YoutubeDL(ydl_opts) as ydl_retry:
                        info = ydl_retry.extract_info(url, download=True)
                        if info is None:
                            raise ValueError(f"更新 cookies 后仍无法提取视频信息。可能需要手动更新 cookies 或检查 URL。URL: {url}")
                else:
                    raise ValueError(f"无法提取视频信息，extract_info 返回 None。可能需要有效的 cookies 或 URL 无效。URL: {url}")

            # 获取下载后的文件路径
            filename = ydl.prepare_filename(info)
            return filename