#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
项目配置文件
集中管理所有可配置的参数
"""

import os
from pathlib import Path


class Config:
    """配置管理类"""

    # 路径配置
    BASE_DIR = Path(__file__).parent
    DOWNLOAD_DIR = BASE_DIR / "downloads"
    OUTPUT_DIR = BASE_DIR / "output"

    # Whisper模型配置（针对faster-whisper优化）
    WHISPER_MODEL_SIZE = "large-v2"  # large-v2 或 distil-whisper-large-v3
    WHISPER_DEVICE = "cuda"  # cuda for GPU, cpu for CPU
    WHISPER_LANGUAGE = None  # None为自动检测，或指定如"zh", "en"
    WHISPER_COMPUTE_TYPE = "int8_float16"  # int8_float16 或 int8
    WHISPER_BATCH_SIZE = 8  # 批次大小，推荐8以加速
    WHISPER_BEAM_SIZE = 5  # 解码宽度
    WHISPER_NUM_WORKERS = 1  # 工作线程数，避免冲突

    # 音频转换配置
    AUDIO_BITRATE = "192k"
    AUDIO_SAMPLE_RATE = 44100
    AUDIO_CHANNELS = 2

    # 下载配置
    DOWNLOAD_FORMAT = "bestaudio/best"
    YOUTUBE_COOKIES_FILE = "./cookies.txt"  # YouTube cookies文件路径，None表示不使用
    YOUTUBE_COOKIES_BROWSER = "firefox"  # 用于获取cookies的浏览器：chrome, firefox, edge, safari
    YOUTUBE_AUTO_UPDATE_COOKIES = True  # 是否自动更新cookies
    USE_PROXY = False  # 是否使用代理
    PROXY_URL = None  # 代理URL，如 "http://proxy.example.com:8080"

    # 文件清理配置
    CLEAN_TEMP_FILES = True
    TEMP_DIRS = ["downloads"]

    @classmethod
    def ensure_dirs(cls):
        """确保必要的目录存在"""
        cls.DOWNLOAD_DIR.mkdir(exist_ok=True)
        cls.OUTPUT_DIR.mkdir(exist_ok=True)

    @classmethod
    def get_model_path(cls):
        """获取模型缓存路径"""
        return cls.BASE_DIR / "models"

    @classmethod
    def get_log_path(cls):
        """获取日志文件路径"""
        return cls.BASE_DIR / "logs" / "app.log"


# 初始化目录
Config.ensure_dirs()