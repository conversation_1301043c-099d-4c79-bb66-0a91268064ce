# -*- coding: utf-8 -*-

"""
视频转文本工具主程序
支持YouTube、Bilibili、抖音视频下载、转换为MP3并转录为文本
"""

import sys
import os
import argparse
from typing import Optional
import yt_dlp

# 尝试导入剪贴板库
try:
    import pyperclip
    CLIPBOARD_AVAILABLE = True
except ImportError:
    CLIPBOARD_AVAILABLE = False
    print("警告: 未安装 pyperclip，无法使用剪贴板功能。请运行 'pip install pyperclip' 安装。")

# 尝试导入通知库
try:
    from plyer import notification
    NOTIFICATION_AVAILABLE = True
except ImportError:
    NOTIFICATION_AVAILABLE = False
    print("警告: 未安装 plyer，无法发送通知。请运行 'pip install plyer' 安装。")
# 强制使用 UTF-8 编码以避免 Windows 控制台中文乱码
try:
    os.environ["PYTHONIOENCODING"] = "utf-8"
except Exception:
    pass

try:
    if hasattr(sys.stdout, "reconfigure"):
        sys.stdout.reconfigure(encoding="utf-8")
    if hasattr(sys.stderr, "reconfigure"):
        sys.stderr.reconfigure(encoding="utf-8")
except Exception:
    pass

if os.name == "nt":
    try:
        import ctypes as _ctypes
        _ctypes.windll.kernel32.SetConsoleOutputCP(65001)
        _ctypes.windll.kernel32.SetConsoleCP(65001)
    except Exception:
        pass

# 添加项目路径到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from downloader.youtube_downloader import YouTubeDownloader
from downloader.bilibili_downloader import BilibiliDownloader
from downloader.douyin_downloader import DouyinDownloader
from converter.audio_converter import AudioConverter
from transcriber.speech_to_text import SpeechToText
from utils.file_utils import FileUtils
from config import Config


from typing import Optional

def main(url_arg: Optional[str] = None):
    print("欢迎使用视频转文本工具！")
    print("支持YouTube、Bilibili、抖音视频下载、转换为MP3并转录为文本")

    while True:
        try:
            # 获取用户输入的视频链接（支持命令行参数与交互输入）
            if url_arg is not None:
                url = url_arg.strip()
                url_arg = None  # 重置，以便下次循环时重新输入
            else:
                if sys.stdin and sys.stdin.isatty():
                    url = input("请输入视频链接 (输入 'exit' 退出): ").strip()
                else:
                    print("检测到非交互环境。请使用 --url 传入视频链接，或在交互终端运行此程序。")
                    return

            if not url:
                print("链接不能为空")
                continue

            if url.lower() == 'exit':
                print("退出程序。")
                break

            # 识别视频来源
            if YouTubeDownloader().validate_url(url):
                downloader = YouTubeDownloader()
                platform = "YouTube"
            elif BilibiliDownloader().validate_url(url):
                downloader = BilibiliDownloader()
                platform = "Bilibili"
            elif DouyinDownloader().validate_url(url):
                downloader = DouyinDownloader()
                platform = "Douyin"
            else:
                print("不支持的视频平台")
                continue

            print(f"检测到{platform}链接，开始处理...")

            # 下载视频
            print("正在下载视频...")
            video_file = downloader.download_video(url)
            print(f"下载完成: {video_file}")

            # 转换为MP3
            print("正在转换为MP3...")
            converter = AudioConverter()
            mp3_file = converter.convert_to_mp3(video_file)
            print(f"MP3转换完成: {mp3_file}")

            # 语音转文本
            print("正在进行语音转文本...")
            transcriber = SpeechToText()
            text_file = mp3_file.replace('.mp3', '.txt')

            # 获取设备信息和加载时间
            device_used, load_time = transcriber._load_model()
            if device_used.lower() == "cuda":
                print(f"🚀 使用GPU (CUDA)进行处理")
            else:
                print(f"💻 使用CPU进行处理")
            print(f"模型加载耗时: {load_time:.2f}s")
            text = transcriber.transcribe_and_save(mp3_file, text_file)
            print(f"转录完成: {text_file}")

            # 将转录文本复制到剪贴板
            if CLIPBOARD_AVAILABLE and text.strip():
                try:
                    pyperclip.copy(text)
                    print("✓ 转录文本已复制到剪贴板")
                except Exception as e:
                    print(f"警告: 复制到剪贴板失败: {e}")
            elif not CLIPBOARD_AVAILABLE:
                print("提示: 安装 pyperclip 可自动复制转录文本到剪贴板")

            # 清理临时文件
            if Config.CLEAN_TEMP_FILES:
                for temp_dir in Config.TEMP_DIRS:
                    FileUtils.clean_temp_files(temp_dir)

            print("\n处理完成！")
            print(f"MP3文件: {mp3_file}")
            print(f"文本文件: {text_file}")

            # 发送通知
            if NOTIFICATION_AVAILABLE:
                try:
                    notification.notify(
                        title="处理完成",
                        message="视频转文本处理已完成",
                        app_name="视频转文本工具"
                    )
                except Exception as e:
                    print(f"发送通知失败: {e}")

            print("\n" + "="*50 + "\n")

        except Exception as e:
            print(f"处理过程中出错: {str(e)}")
            continue


# cookies 自动更新逻辑已移动到 __main__ 入口处，并统一使用 tuple 传参以兼容 yt_dlp 解析

if __name__ == "__main__":
    import argparse as _argparse  # 局部导入以避免顶层污染

    parser = _argparse.ArgumentParser(description="视频转文本工具")
    parser.add_argument("--url", help="视频链接", default=None)
    parser.add_argument("--no-auto-cookies", action="store_true", help="禁用启动时自动从浏览器更新 YouTube cookies")
    args = parser.parse_args()

    # 启动时自动更新 cookies（可通过 --no-auto-cookies 禁用）
    if Config.YOUTUBE_AUTO_UPDATE_COOKIES and not args.no_auto_cookies:
        # 统一使用 tuple 传参，避免字符串被当作字符序列解析
        browsers = []
        firefox_profile = os.getenv('FIREFOX_PROFILE')
        if firefox_profile:
            browsers.append(('firefox', firefox_profile))
        else:
            browsers.append(('firefox',))
        browsers.extend([
            ('edge',),
            ('chromium',),
            ('chrome',),
        ])

        cookie_output = 'cookies.txt'
        cookies_updated = False

        for c_browser in browsers:
            try:
                print(f'正在从 {c_browser[0]} 浏览器更新 cookies...')
                print(f'cookiesfrombrowser 参数: {repr(c_browser)} -> 输出文件: {cookie_output}')
                ydl_opts = {
                    'quiet': True,
                    'skip_download': True,
                    'cookiesfrombrowser': c_browser,  # 必须是 tuple，避免被当作字符序列
                    'cookiefile': cookie_output,
                    'no_warnings': True,
                }
                with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                    ydl.extract_info('https://www.youtube.com/', download=False)
                if os.path.exists(cookie_output):
                    print(f'已保存 cookies -> {cookie_output}')
                    cookies_updated = True
                    break
            except Exception as e:
                print(f'浏览器配置 {c_browser} 失败: {type(e).__name__}: {e!r}')

        if not cookies_updated:
            print('自动从浏览器更新 cookies 失败，请确保浏览器已完全关闭或手动导出 cookies 到 cookies.txt。')

    main(args.url)