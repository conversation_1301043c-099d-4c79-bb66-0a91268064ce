#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
文件处理工具模块
提供文件操作、路径处理等通用功能
"""

import os
import shutil
from pathlib import Path


class FileUtils:
    """文件处理工具类"""

    @staticmethod
    def ensure_dir(directory):
        """确保目录存在，如果不存在则创建"""
        Path(directory).mkdir(parents=True, exist_ok=True)

    @staticmethod
    def get_file_size(file_path):
        """获取文件大小（字节）"""
        return os.path.getsize(file_path) if os.path.exists(file_path) else 0

    @staticmethod
    def clean_temp_files(temp_dir):
        """清理临时文件（Windows 兼容，忽略占用/只读导致的删除错误）"""
        if not os.path.exists(temp_dir):
            return

        def _on_rm_error(func, path, exc_info):
            # 遇到只读/占用时尝试去掉只读并重试一次；仍失败则忽略但打印
            import stat
            try:
                os.chmod(path, stat.S_IWRITE)
                func(path)
            except Exception as e:
                print(f"删除失败(忽略): {path} -> {e}")

        try:
            shutil.rmtree(temp_dir, onerror=_on_rm_error)
            print(f"已清理临时目录: {temp_dir}")
        except Exception as e:
            # 最后兜底：不让异常冒泡以免中断主流程
            print(f"清理临时目录失败(忽略): {temp_dir} -> {e}")

    @staticmethod
    def generate_filename(title, extension="mp3"):
        """根据视频标题生成文件名"""
        # 移除非法字符
        safe_title = "".join(c for c in title if c.isalnum() or c in (' ', '-', '_')).rstrip()
        return f"{safe_title}.{extension}"

    @staticmethod
    def get_output_path(output_dir, filename):
        """获取输出文件完整路径"""
        FileUtils.ensure_dir(output_dir)
        return os.path.join(output_dir, filename)