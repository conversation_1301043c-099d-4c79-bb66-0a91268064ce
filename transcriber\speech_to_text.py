#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
语音转文本模块
负责将音频转换为文本
"""

import os
import time
from typing import Optional, Callable, Tuple
from faster_whisper import WhisperModel
from config import Config

# 尝试导入批处理推理管线（仅在支持的 faster-whisper 版本可用）
try:
    from faster_whisper import BatchedInferencePipeline as _BatchedInferencePipeline
except Exception:
    _BatchedInferencePipeline = None


class SpeechToText:
    """
    语音转文本类
    使用faster-whisper进行语音识别
    """

    def __init__(self, model_size: Optional[str] = None, device: Optional[str] = None):
        """
        初始化语音转文本器

        Args:
            model_size: Whisper模型大小 (tiny, base, small, medium, large)，None使用配置文件默认值
            device: 运行设备 (auto, cpu, cuda)，None使用配置文件默认值
        """
        self.model_size = model_size or Config.WHISPER_MODEL_SIZE
        self.device = device or Config.WHISPER_DEVICE
        self.compute_type = getattr(Config, 'WHISPER_COMPUTE_TYPE', 'float16')
        self.batch_size = getattr(Config, 'WHISPER_BATCH_SIZE', 8)
        self.beam_size = getattr(Config, 'WHISPER_BEAM_SIZE', 5)
        self.num_workers = getattr(Config, 'WHISPER_NUM_WORKERS', 1)
        self.chunk_length = getattr(Config, 'WHISPER_CHUNK_LENGTH', None)  # 可选：每块秒数（如 30 或 60）
        self.model = None
        self.batched_pipe = None  # 当支持 BatchedInferencePipeline 时启用

    def _load_model(self) -> Tuple[str, float]:
        """加载Whisper模型

        Returns:
            Tuple[str, float]: (使用的设备, 加载耗时)
        """
        if self.model is None:
            print(f"加载Whisper模型 ({self.model_size})...")
            start_time = time.time()
            # 直接尝试加载模型；若 GPU 不可用将在下面的 try/except 中自动降级到 CPU+int8

            def _init_model(device, compute_type):
                return WhisperModel(
                    self.model_size,
                    device=device,
                    compute_type=compute_type,
                    num_workers=self.num_workers
                )
            try:
                self.model = _init_model(self.device, self.compute_type)
                actual_device = self.device
            except Exception as e:
                err = str(e).lower()
                print(f"首次加载失败 {type(e).__name__}: {e}")
                # 如检测到 CUDA/cuDNN 相关问题，则自动降级到 CPU+int8 重试
                if self.device == "cuda" or "cudnn" in err or "cuda" in err or "cublas" in err:
                    print("检测到 GPU 环境不可用，自动切换到 CPU + int8 重试...")
                    self.device = "cpu"
                    self.compute_type = "int8"
                    os.environ["CT2_FORCE_CPU"] = "1"
                    self.model = _init_model(self.device, self.compute_type)
                    actual_device = "cpu"
                else:
                    raise
            # 若可用且 batch_size>1，则初始化批处理推理管线
            if _BatchedInferencePipeline is not None and (self.batch_size or 0) > 1:
                try:
                    self.batched_pipe = _BatchedInferencePipeline(model=self.model)
                    print(f"已启用批处理推理管线 (batch_size={self.batch_size})")
                except Exception as e:
                    print(f"批处理推理管线初始化失败，将回退到单模型: {e}")
                    self.batched_pipe = None
            load_time = time.time() - start_time
            print(f"模型加载耗时: {load_time:.2f}s")
            return actual_device, load_time
        return self.device, 0.0

    def transcribe_audio(self,
                        audio_file: str,
                        language: Optional[str] = None,
                        progress_callback: Optional[Callable] = None) -> Tuple[str, float]:
        """
        将音频文件转换为文本

        Args:
            audio_file: 音频文件路径
            language: 语言代码 (如 'zh' for Chinese, 'en' for English)，None为自动检测
            progress_callback: 进度回调函数

        Returns:
            str: 识别出的文本

        Raises:
            FileNotFoundError: 如果音频文件不存在
            RuntimeError: 如果转录失败
        """
        # 检查文件是否存在
        if not os.path.exists(audio_file):
            raise FileNotFoundError(f"音频文件不存在: {audio_file}")

        # 加载模型
        device_used, load_time = self._load_model()

        try:
            print(f"开始转录音频文件: {audio_file}")
            transcribe_start_time = time.time()

            def _do_transcribe():
                if self.batched_pipe is not None:
                    print(f"使用批处理推理（batch_size={self.batch_size}）...")
                    _kwargs = {}
                    if self.chunk_length is not None:
                        _kwargs['chunk_length'] = self.chunk_length
                    return self.batched_pipe.transcribe(
                        audio_file,
                        batch_size=self.batch_size,
                        language=language or Config.WHISPER_LANGUAGE,
                        **_kwargs
                    )
                else:
                    return self.model.transcribe(
                        audio_file,
                        language=language or Config.WHISPER_LANGUAGE,
                        beam_size=self.beam_size,
                        vad_filter=True,
                        vad_parameters=dict(threshold=0.5, min_speech_duration_ms=250),
                        chunk_length=self.chunk_length
                    )

            try:
                segments, info = _do_transcribe()
            except Exception as e1:
                err1 = str(e1).lower()
                if "cudnn" in err1 or "cuda" in err1 or "cublas" in err1:
                    print("检测到推理阶段 GPU/cuDNN 异常，自动切换到 CPU + int8 重试...")
                    self.device = "cpu"
                    self.compute_type = "int8"
                    os.environ["CT2_FORCE_CPU"] = "1"
                    # 重新初始化模型，并禁用批处理管线以避免沿用 GPU 上下文
                    self.model = None
                    self.batched_pipe = None
                    device_used, _ = self._load_model()
                    segments, info = self.model.transcribe(
                        audio_file,
                        language=language or Config.WHISPER_LANGUAGE,
                        beam_size=self.beam_size,
                        vad_filter=True,
                        vad_parameters=dict(threshold=0.5, min_speech_duration_ms=250),
                        chunk_length=self.chunk_length
                    )
                else:
                    raise

            # 收集所有文本片段
            text_segments = []
            for segment in segments:
                text_segments.append(segment.text)
                if progress_callback:
                    pass

            full_text = " ".join(text_segments).strip()
            transcribe_time = time.time() - transcribe_start_time
            print(f"转录完成，检测语言: {info.language}")
            print(f"转录耗时: {transcribe_time:.2f}s")
            return full_text, transcribe_time

        except Exception as e:
            raise RuntimeError(f"语音转文本失败: {str(e)}")

    def save_transcription(self, text: str, output_file: str):
        """
        保存转录文本到文件

        Args:
            text: 转录文本
            output_file: 输出文件路径
        """
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(text)
        print(f"转录文本已保存到: {output_file}")

    def transcribe_and_save(self,
                           audio_file: str,
                           output_file: str,
                           language: Optional[str] = None) -> str:
        """
        转录音频并保存到文件

        Args:
            audio_file: 音频文件路径
            output_file: 输出文本文件路径
            language: 语言代码

        Returns:
            str: 识别出的文本
        """
        text, transcribe_time = self.transcribe_audio(audio_file, language)
        self.save_transcription(text, output_file)
        return text