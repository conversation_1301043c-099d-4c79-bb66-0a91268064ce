# 网络平台视频转文本CLI工具

一个能够以[yt-dlp](https://github.com/yt-dlp/yt-dlp)从YouTube、Bilibili、抖音等视频平台下载视频，转换为MP3格式，并使用[faster-Whisper](https://github.com/SYSTRAN/faster-whisper)进行stt(语音转文本)的CLI工具。

## 系统要求

- Python 3.8+
- FFmpeg（用于音频处理）
- 至少4GB RAM（推荐8GB+）
- 支持CUDA的GPU（可选，用于加速Whisper模型）

## 安装步骤


### 2. 安装Python依赖

```bash
pip install -r requirements.txt
```

### 3. 安装FFmpeg

#### Windows:

```bash
choco install ffmpeg
```

#### macOS:
```bash
brew install ffmpeg
```

#### Linux:
```bash
sudo apt update
sudo apt install ffmpeg
```

## GPU 加速（可选）
- 需要安装cuda，12.9可以 https://developer.nvidia.com/cuda-toolkit-archive，
- cudnn,版本不拘 https://developer.nvidia.com/cudnn ，
- 若仍有异常，参见 https://github.com/SYSTRAN/faster-whisper?tab=readme-ov-file#gpu


## Whisper模型下载

项目使用faster-whisper进行语音转文本，首次运行时会自动下载模型文件，但比较慢。

### 手动下载（推荐大文件）
如果网络不稳定或想使用多线程下载，可以手动下载模型文件：

#### 1. 模型文件信息
- **模型**: Systran/faster-whisper-large-v2
- **下载地址**: https://huggingface.co/Systran/faster-whisper-large-v2
- **主要文件**:
  - `model.bin` (~3GB) - 主要模型文件
  - `config.json` - 配置文件
  - `tokenizer.json` (~2MB) - 分词器
  - `vocabulary.txt` - 词汇表
#### 3. 文件放置位置
下载完成后，将文件放置到以下目录：

**Windows默认位置**:
```
C:\Users\<USER>\.cache\huggingface\hub\models--Systran--faster-whisper-large-v2\snapshots\[版本号]\
```

**自定义缓存位置**:
设置环境变量 `HF_HUB_CACHE` 到你想要的目录。

#### 4. 验证安装
运行以下Python代码验证：
```python
from faster_whisper import WhisperModel
model = WhisperModel("large-v2")
print("模型加载成功！")
```
## 使用方法

### 基本使用

1. 运行主程序：
```bash
python main.py
```
3. 等待处理完成：
   - 下载视频
   - 转换为MP3
   - 语音转文本
   - 生成输出文件

### 支持的链接格式

- YouTube: `https://www.youtube.com/watch?v=...` 或 `https://youtu.be/...`
- Bilibili: `https://www.bilibili.com/video/...`
- 抖音: `https://www.douyin.com/video/...`

## 输出文件

处理完成后会在以下位置生成文件：

- `output/` 目录：MP3音频文件
- 同目录：对应的 `.txt` 文本文件

例如：
- `output/视频标题.mp3`
- `output/视频标题.txt`

## 配置选项

项目的所有配置都集中在 `config.py` 文件中。你可以通过修改这个文件来调整各种参数：

### Whisper模型配置
- `WHISPER_MODEL_SIZE`: 模型大小 (tiny, base, small, medium, large)
- `WHISPER_DEVICE`: 运行设备 (auto, cpu, cuda)
- `WHISPER_LANGUAGE`: 语言设置 (None为自动检测)

### 音频配置
- `AUDIO_BITRATE`: MP3比特率
- `AUDIO_SAMPLE_RATE`: 采样率
- `AUDIO_CHANNELS`: 声道数

### 路径配置
- `DOWNLOAD_DIR`: 下载目录
- `OUTPUT_DIR`: 输出目录

### 其他配置
- `DOWNLOAD_FORMAT`: 下载格式
- `CLEAN_TEMP_FILES`: 是否清理临时文件

### 示例修改配置

```python
# 在 config.py 中修改
WHISPER_MODEL_SIZE = "small"  # 使用更准确的模型
AUDIO_BITRATE = "320k"        # 更高音质
```

默认模型大小为 `base`，提供速度和准确性的良好平衡。

## 注意事项

1. **首次运行**：Whisper模型下载可能需要几分钟
2. **网络要求**：需要稳定的网络连接下载视频
3. **存储空间**：确保有足够的磁盘空间存储下载的视频和音频
4. **版权提醒**：请确保您有权下载和处理相关内容
5. **性能**：长视频处理时间较长，取决于硬件配置

## 故障排除

### FFmpeg未找到
```
RuntimeError: FFmpeg未安装或不可用
```
解决：确保FFmpeg已正确安装并添加到PATH

### 模型下载失败
```
ConnectionError: 无法下载模型
```
解决：检查网络连接，重试或使用代理

### 内存不足
```
MemoryError: 内存不足
```
解决：使用更小的模型大小或增加系统内存

## 项目结构

```
ytb/
├── main.py                 # 主程序入口
├── config.py              # 配置文件
├── requirements.txt        # 项目依赖
├── README.md              # 使用说明
├── downloader/            # 下载模块
│   ├── __init__.py
│   ├── youtube_downloader.py
│   ├── bilibili_downloader.py
│   └── douyin_downloader.py
├── converter/             # 转换模块
│   ├── __init__.py
│   └── audio_converter.py
├── transcriber/           # 转录模块
│   ├── __init__.py
│   └── speech_to_text.py
└── utils/                 # 工具模块
    ├── __init__.py
    └── file_utils.py
```

## 许可证

请查看项目许可证文件。

## 贡献

欢迎提交问题和改进建议！

## 更新日志

- v1.0.0: 初始版本，支持基础功能