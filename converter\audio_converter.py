#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
音频转换器模块
负责将视频文件转换为MP3格式
"""

import os
import subprocess
from typing import Optional, Callable
from config import Config


class AudioConverter:
    """
    音频转换器类
    用于将视频文件转换为MP3格式
    """
    
    def __init__(self, output_dir: Optional[str] = None):
        """
        初始化转换器

        Args:
            output_dir: 输出文件保存目录，None使用配置文件默认值
        """
        self.output_dir = str(output_dir or Config.OUTPUT_DIR)

        # 确保输出目录存在
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
    
    def _check_ffmpeg(self) -> bool:
        """
        检查FFmpeg是否可用
        
        Returns:
            bool: FFmpeg是否可用
        """
        try:
            subprocess.run(
                ["ffmpeg", "-version"], 
                stdout=subprocess.PIPE, 
                stderr=subprocess.PIPE,
                check=True
            )
            return True
        except (subprocess.SubprocessError, FileNotFoundError):
            return False
    
    def convert_to_mp3(self,
                        input_file: str,
                        bitrate: Optional[str] = None,
                        progress_callback: Optional[Callable] = None) -> str:
        """
        将视频文件转换为MP3格式
        
        Args:
            input_file: 输入视频文件路径
            bitrate: MP3比特率，默认192k
            progress_callback: 进度回调函数，接收转换进度百分比
            
        Returns:
            str: 输出MP3文件路径
            
        Raises:
            RuntimeError: 如果FFmpeg不可用或转换失败
        """
        # 检查FFmpeg是否可用
        if not self._check_ffmpeg():
            raise RuntimeError("FFmpeg未安装或不可用，请安装FFmpeg后再试")
        
        # 检查输入文件是否存在
        if not os.path.exists(input_file):
            raise FileNotFoundError(f"输入文件不存在: {input_file}")
        
        # 生成输出文件名
        filename = os.path.basename(input_file)
        name_without_ext = os.path.splitext(filename)[0]
        output_file = os.path.join(self.output_dir, f"{name_without_ext}.mp3")
        
        # 使用配置中的参数
        bitrate = bitrate or Config.AUDIO_BITRATE

        # 构建FFmpeg命令（加入 -y 覆盖已存在文件，避免卡住）
        cmd = [
            "ffmpeg",
            "-y",  # 直接覆盖输出文件
            "-i", input_file,  # 输入文件
            "-vn",  # 不包含视频
            "-ar", str(Config.AUDIO_SAMPLE_RATE),  # 采样率
            "-ac", str(Config.AUDIO_CHANNELS),  # 声道数
            "-b:a", bitrate,  # 音频比特率
            "-f", "mp3",  # 输出格式
            output_file  # 输出文件
        ]

        # 执行转换
        try:
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                universal_newlines=True
            )
            
            # 等待进程完成
            stdout, stderr = process.communicate()
            
            # 检查是否成功
            if process.returncode != 0:
                raise RuntimeError(f"转换失败: {stderr}")
            
            return output_file
            
        except subprocess.SubprocessError as e:
            raise RuntimeError(f"转换过程中出错: {str(e)}")
    
    def extract_audio(self, input_file: str) -> str:
        """
        从视频文件中提取音频（不改变格式）
        
        Args:
            input_file: 输入视频文件路径
            
        Returns:
            str: 输出音频文件路径
        """
        # 检查FFmpeg是否可用
        if not self._check_ffmpeg():
            raise RuntimeError("FFmpeg未安装或不可用，请安装FFmpeg后再试")
        
        # 检查输入文件是否存在
        if not os.path.exists(input_file):
            raise FileNotFoundError(f"输入文件不存在: {input_file}")
        
        # 生成输出文件名
        filename = os.path.basename(input_file)
        name_without_ext = os.path.splitext(filename)[0]
        output_file = os.path.join(self.output_dir, f"{name_without_ext}.aac")
        
        # 构建FFmpeg命令
        cmd = [
            "ffmpeg",
            "-i", input_file,  # 输入文件
            "-vn",  # 不包含视频
            "-acodec", "copy",  # 复制音频编码（不重新编码）
            output_file  # 输出文件
        ]
        
        # 执行提取
        try:
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                universal_newlines=True
            )
            
            # 等待进程完成
            stdout, stderr = process.communicate()
            
            # 检查是否成功
            if process.returncode != 0:
                raise RuntimeError(f"提取失败: {stderr}")
            
            return output_file
            
        except subprocess.SubprocessError as e:
            raise RuntimeError(f"提取过程中出错: {str(e)}")